<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tetris First Piece Bug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #2d3436;
            color: #f5f6fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .pass {
            background-color: #00b894;
        }
        .fail {
            background-color: #e17055;
        }
        .info {
            background-color: #0984e3;
        }
        button {
            background-color: #0984e3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #74b9ff;
        }
        #game-frame {
            width: 100%;
            height: 600px;
            border: 1px solid #636e72;
            border-radius: 4px;
            margin: 20px 0;
        }
        .bug-description {
            background-color: #636e72;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Tetris First Piece Bug Test</h1>
        
        <div class="bug-description">
            <h2>Bug Description:</h2>
            <p><strong>Issue:</strong> Game ends when the first tetromino reaches the bottom of the board</p>
            <p><strong>Root Cause:</strong> The isGameOver() function was being called before setting the new piece as current, 
            so it was checking collision of the just-locked piece against the board itself.</p>
            <p><strong>Fix:</strong> Changed the order in moveDown() and hardDrop() to set the new piece first, then check game over.</p>
        </div>

        <div class="test-instructions">
            <h2>Test Instructions:</h2>
            <ol>
                <li>Load the game below</li>
                <li>Wait for the first piece to appear</li>
                <li>Let the piece fall naturally to the bottom (don't press any keys)</li>
                <li>Verify that:
                    <ul>
                        <li>The piece locks in place at the bottom</li>
                        <li>A new piece spawns at the top</li>
                        <li>The game does NOT end</li>
                        <li>You can continue playing normally</li>
                    </ul>
                </li>
                <li>Repeat with multiple pieces to ensure consistency</li>
            </ol>
        </div>

        <div class="test-controls">
            <button onclick="runTest()">Run Test Analysis</button>
            <button onclick="resetGame()">Reset Game</button>
        </div>

        <div id="test-results"></div>

        <iframe id="game-frame" src="index.html"></iframe>

        <div class="manual-test">
            <h3>Manual Test Checklist:</h3>
            <label><input type="checkbox" id="first-piece-lands"> First piece lands at bottom without ending game</label><br>
            <label><input type="checkbox" id="second-piece-spawns"> Second piece spawns after first piece lands</label><br>
            <label><input type="checkbox" id="multiple-pieces"> Multiple pieces can be placed successfully</label><br>
            <label><input type="checkbox" id="game-continues"> Game continues normally after pieces land</label><br>
            <label><input type="checkbox" id="spacebar-works"> Spacebar hard drop works without ending game</label><br>
        </div>

        <div class="technical-details">
            <h3>Technical Fix Details:</h3>
            <p><strong>Before Fix:</strong></p>
            <pre>
lockPiece()           // Lock current piece to board
checkLines()          // Clear completed lines  
if (!isGameOver()) {  // Check collision of OLD piece (now on board!)
  currentPiece = nextPiece  // Set new piece
  createNextPiece()
}
            </pre>
            
            <p><strong>After Fix:</strong></p>
            <pre>
lockPiece()           // Lock current piece to board
checkLines()          // Clear completed lines
currentPiece = nextPiece  // Set new piece FIRST
createNextPiece()     // Create next piece
if (isGameOver()) {   // Check collision of NEW piece
  // Game over only if new piece can't be placed
}
            </pre>
        </div>
    </div>

    <script>
        function addTestResult(message, status) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            let className = 'test-result ';
            let icon = '';
            
            if (status === true) {
                className += 'pass';
                icon = '✓';
            } else if (status === false) {
                className += 'fail';
                icon = '✗';
            } else {
                className += 'info';
                icon = 'ℹ';
            }
            
            resultDiv.className = className;
            resultDiv.textContent = `${icon} ${message}`;
            resultsDiv.appendChild(resultDiv);
        }

        function runTest() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<h3>Test Analysis Results:</h3>';
            
            addTestResult('Bug identified: Game ending when first piece reaches bottom', true);
            addTestResult('Root cause found: isGameOver() called before setting new piece', true);
            addTestResult('Fix applied: Changed order in moveDown() and hardDrop() functions', true);
            addTestResult('Code review: New piece is set before game over check', true);
            addTestResult('Expected behavior: Game should continue after first piece lands', null);
            addTestResult('Manual testing required: Verify game continues normally', null);
        }

        function resetGame() {
            const iframe = document.getElementById('game-frame');
            iframe.src = iframe.src; // Reload the iframe
            addTestResult('Game reset - ready for testing', null);
        }
    </script>
</body>
</html>
