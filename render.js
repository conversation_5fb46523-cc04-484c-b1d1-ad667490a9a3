// Canvas setup
const canvas = document.getElementById('game-board')
const ctx = canvas.getContext('2d')
const nextCanvas = document.getElementById('next-piece')
const nextCtx = nextCanvas.getContext('2d')

// Scale canvas to match CSS size
canvas.width = canvas.offsetWidth
canvas.height = canvas.offsetHeight
nextCanvas.width = nextCanvas.offsetWidth
nextCanvas.height = nextCanvas.offsetHeight

// Colors for tetrominoes
const COLORS = [
  '#00cec9', // I - Cyan
  '#fdcb6e', // O - Yellow
  '#6c5ce7', // T - Purple
  '#fd79a8', // L - Pink
  '#0984e3', // J - Blue
  '#00b894', // S - Green
  '#e17055', // Z - Orange
]

// Draw the game board
function drawBoard() {
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // Draw existing blocks
  for (let y = 0; y < ROWS; y++) {
    for (let x = 0; x < COLS; x++) {
      if (board[y][x] !== EMPTY) {
        drawBlock(x, y, COLORS[board[y][x] - 1])
      }
    }
  }

  // Draw current piece
  if (currentPiece) {
    currentPiece.shape.forEach((row, y) => {
      row.forEach((value, x) => {
        if (value) {
          drawBlock(
            currentPiece.x + x,
            currentPiece.y + y,
            COLORS[
              SHAPES.findIndex(
                (shape) =>
                  JSON.stringify(shape) === JSON.stringify(currentPiece.shape),
              )
            ],
          )
        }
      })
    })
  }

  // Draw grid lines
  ctx.strokeStyle = '#636e72'
  ctx.lineWidth = 0.5
  for (let i = 0; i <= COLS; i++) {
    ctx.beginPath()
    ctx.moveTo(i * BLOCK_SIZE, 0)
    ctx.lineTo(i * BLOCK_SIZE, ROWS * BLOCK_SIZE)
    ctx.stroke()
  }
  for (let i = 0; i <= ROWS; i++) {
    ctx.beginPath()
    ctx.moveTo(0, i * BLOCK_SIZE)
    ctx.lineTo(COLS * BLOCK_SIZE, i * BLOCK_SIZE)
    ctx.stroke()
  }
}

// Draw next piece preview
function drawNextPiece() {
  nextCtx.clearRect(0, 0, nextCanvas.width, nextCanvas.height)

  if (nextPiece) {
    const centerX =
      (nextCanvas.width / BLOCK_SIZE - nextPiece.shape[0].length) / 2
    const centerY =
      (nextCanvas.height / BLOCK_SIZE - nextPiece.shape.length) / 2

    nextPiece.shape.forEach((row, y) => {
      row.forEach((value, x) => {
        if (value) {
          nextCtx.fillStyle =
            COLORS[
              SHAPES.findIndex(
                (shape) =>
                  JSON.stringify(shape) === JSON.stringify(nextPiece.shape),
              )
            ]
          nextCtx.fillRect(
            (centerX + x) * BLOCK_SIZE,
            (centerY + y) * BLOCK_SIZE,
            BLOCK_SIZE - 1,
            BLOCK_SIZE - 1,
          )
        }
      })
    })
  }
}

// Draw a single block
function drawBlock(x, y, color) {
  ctx.fillStyle = color
  ctx.fillRect(x * BLOCK_SIZE, y * BLOCK_SIZE, BLOCK_SIZE - 1, BLOCK_SIZE - 1)

  // Add highlight effect
  ctx.fillStyle = 'rgba(255, 255, 255, 0.2)'
  ctx.fillRect(x * BLOCK_SIZE, y * BLOCK_SIZE, BLOCK_SIZE - 1, 2)
}

// Animation loop
let lastTime = 0
function update(time = 0) {
  const deltaTime = time - lastTime
  lastTime = time

  // Game speed based on level (ms per frame)
  const speed = Math.max(100, 1000 - level * 100)

  if (deltaTime > speed) {
    moveDown()
  }

  drawBoard()
  drawNextPiece()
  if (!gameOver) {
    requestAnimationFrame(update)
  } else {
    // Game over handling
    ctx.fillStyle = 'rgba(0, 0, 0, 0.75)'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    ctx.fillStyle = '#fff'
    ctx.font = '30px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('GAME OVER', canvas.width / 2, canvas.height / 2)
  }
}

// Start rendering when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  requestAnimationFrame(update)
})
