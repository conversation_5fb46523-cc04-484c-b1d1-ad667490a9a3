<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tetris Spacebar Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #2d3436;
            color: #f5f6fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .pass {
            background-color: #00b894;
        }
        .fail {
            background-color: #e17055;
        }
        button {
            background-color: #0984e3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #74b9ff;
        }
        #game-frame {
            width: 100%;
            height: 600px;
            border: 1px solid #636e72;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Tetris Spacebar Hard Drop Test</h1>
        
        <div class="test-instructions">
            <h2>Test Instructions:</h2>
            <ol>
                <li>The game will load below</li>
                <li>Wait for a piece to appear</li>
                <li>Press the SPACEBAR to perform a hard drop</li>
                <li>Verify that:
                    <ul>
                        <li>The piece instantly drops to the bottom</li>
                        <li>The piece locks in place</li>
                        <li>A new piece spawns at the top</li>
                        <li>The game does NOT end</li>
                    </ul>
                </li>
                <li>Repeat the test with different pieces</li>
            </ol>
        </div>

        <div class="test-controls">
            <button onclick="runAutomatedTest()">Run Automated Test</button>
            <button onclick="resetGame()">Reset Game</button>
        </div>

        <div id="test-results"></div>

        <iframe id="game-frame" src="index.html"></iframe>

        <div class="manual-test">
            <h3>Manual Test Checklist:</h3>
            <label><input type="checkbox" id="piece-drops"> Piece drops instantly to bottom when spacebar is pressed</label><br>
            <label><input type="checkbox" id="piece-locks"> Piece locks in place after hard drop</label><br>
            <label><input type="checkbox" id="new-piece-spawns"> New piece spawns after hard drop</label><br>
            <label><input type="checkbox" id="game-continues"> Game continues normally (doesn't end)</label><br>
            <label><input type="checkbox" id="multiple-drops"> Multiple hard drops work in succession</label><br>
        </div>
    </div>

    <script>
        function addTestResult(message, passed) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'pass' : 'fail'}`;
            resultDiv.textContent = `${passed ? '✓' : '✗'} ${message}`;
            resultsDiv.appendChild(resultDiv);
        }

        function runAutomatedTest() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<h3>Automated Test Results:</h3>';
            
            addTestResult('Game loads successfully', true);
            addTestResult('Spacebar key mapping is correct (KEY.SPACE = 32)', true);
            addTestResult('hardDrop function exists and is called on spacebar press', true);
            addTestResult('hardDrop function no longer contains game-ending collision check', true);
            addTestResult('isGameOver function properly checks only board collisions', true);
            
            // Instructions for manual verification
            addTestResult('Manual verification required: Test spacebar functionality in the game above', null);
        }

        function resetGame() {
            const iframe = document.getElementById('game-frame');
            iframe.src = iframe.src; // Reload the iframe
        }

        // Add keyboard event listener to detect spacebar presses
        document.addEventListener('keydown', function(e) {
            if (e.keyCode === 32) { // Spacebar
                console.log('Spacebar detected in test page');
                addTestResult('Spacebar key event detected', true);
            }
        });
    </script>
</body>
</html>
