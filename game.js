// Game Constants
const COLS = 10
const ROWS = 20
const BLOCK_SIZE = 30
const EMPTY = 0

// Tetromino Shapes
const SHAPES = [
  [
    [0, 0, 0, 0],
    [1, 1, 1, 1],
    [0, 0, 0, 0],
    [0, 0, 0, 0],
  ], // I
  [
    [1, 1],
    [1, 1],
  ], // O
  [
    [0, 1, 0],
    [1, 1, 1],
    [0, 0, 0],
  ], // T
  [
    [0, 0, 1],
    [1, 1, 1],
    [0, 0, 0],
  ], // L
  [
    [1, 0, 0],
    [1, 1, 1],
    [0, 0, 0],
  ], // J
  [
    [0, 1, 1],
    [1, 1, 0],
    [0, 0, 0],
  ], // S
  [
    [1, 1, 0],
    [0, 1, 1],
    [0, 0, 0],
  ], // Z
]

// Game State
let board = Array(ROWS)
  .fill()
  .map(() => Array(COLS).fill(EMPTY))
let currentPiece = null
let nextPiece = null
let score = 0
let level = 1
let gameOver = false

// Initialize game
function init() {
  createNewPiece()
  createNextPiece()
}

// Create a new random tetromino
function createNewPiece() {
  currentPiece = {
    shape: SHAPES[Math.floor(Math.random() * SHAPES.length)],
    x: Math.floor(COLS / 2) - 1,
    y: 0,
  }
}

// Create next piece preview
function createNextPiece() {
  nextPiece = {
    shape: SHAPES[Math.floor(Math.random() * SHAPES.length)],
    x: 0,
    y: 0,
  }
}

// Check for collisions
function isCollision(offsetX = 0, offsetY = 0, piece = currentPiece) {
  for (let y = 0; y < piece.shape.length; y++) {
    for (let x = 0; x < piece.shape[y].length; x++) {
      if (piece.shape[y][x] !== 0) {
        const newX = piece.x + x + offsetX
        const newY = piece.y + y + offsetY

        if (newX < 0 || newX >= COLS || newY >= ROWS) {
          return true
        }

        if (newY >= 0 && board[newY][newX] !== EMPTY) {
          return true
        }
      }
    }
  }
  return false
}

// Lock piece to board
function lockPiece() {
  for (let y = 0; y < currentPiece.shape.length; y++) {
    for (let x = 0; x < currentPiece.shape[y].length; x++) {
      if (currentPiece.shape[y][x] !== 0) {
        board[currentPiece.y + y][currentPiece.x + x] = 1
      }
    }
  }
}

// Check for completed lines
function checkLines() {
  let linesCleared = 0
  for (let y = ROWS - 1; y >= 0; y--) {
    if (board[y].every((cell) => cell !== EMPTY)) {
      // Remove line
      board.splice(y, 1)
      // Add new empty line at top
      board.unshift(Array(COLS).fill(EMPTY))
      linesCleared++
      y++ // Check same row again after shift
    }
  }

  if (linesCleared > 0) {
    // Update score
    const points = [0, 40, 100, 300, 1200][linesCleared] * level
    score += points
    document.getElementById('score').textContent = score

    // Level up every 10 lines
    if (score >= level * 1000) {
      level++
      document.getElementById('level').textContent = level
    }
  }
}

// Rotate current piece
function rotate() {
  const rotated = []
  for (let x = 0; x < currentPiece.shape[0].length; x++) {
    const newRow = []
    for (let y = currentPiece.shape.length - 1; y >= 0; y--) {
      newRow.push(currentPiece.shape[y][x])
    }
    rotated.push(newRow)
  }

  const originalShape = currentPiece.shape
  currentPiece.shape = rotated

  // Wall kick - try adjusting position if rotation causes collision
  if (isCollision()) {
    currentPiece.shape = originalShape
  }
}

// Game loop functions to be implemented
function moveDown() {
  if (!isCollision(0, 1)) {
    currentPiece.y++
    return true
  }
  lockPiece()
  checkLines()
  if (!isGameOver()) {
    currentPiece = nextPiece
    createNextPiece()
  }
  return false
}

function moveLeft() {
  if (!isCollision(-1, 0)) {
    currentPiece.x--
  }
}

function moveRight() {
  if (!isCollision(1, 0)) {
    currentPiece.x++
  }
}

function isGameOver() {
  if (isCollision(0, 0)) {
    gameOver = true
    return true
  }
  return false
}

// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', init)
