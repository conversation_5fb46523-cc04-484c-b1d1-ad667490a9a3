// Key codes for controls
const KEY = {
  LEFT: 37,
  RIGHT: 39,
  DOWN: 40,
  UP: 38,
  SPACE: 32,
  P: 80,
}

// Input handling
let isPaused = false
let lastInputTime = 0
const INPUT_DELAY = 100 // ms between allowed inputs

// Initialize input listeners
function initInput() {
  document.addEventListener('keydown', handleKeyDown)

  // Touch controls for mobile
  if ('ontouchstart' in window) {
    initTouchControls()
  }
}

// Handle keyboard input
function handleKeyDown(e) {
  const now = Date.now()
  if (now - lastInputTime < INPUT_DELAY) return
  lastInputTime = now

  if (e.keyCode === KEY.P) {
    togglePause()
    return
  }

  if (isPaused || gameOver) return

  switch (e.keyCode) {
    case KEY.LEFT:
      moveLeft()
      break
    case KEY.RIGHT:
      moveRight()
      break
    case KEY.DOWN:
      moveDown()
      break
    case KEY.UP:
      rotate()
      break
    case KEY.SPACE:
      hardDrop()
      break
  }
}

// Drop piece instantly to bottom
function hardDrop() {
  if (isCollision(0, 0)) {
    // Can't move at all - game over
    gameOver = true
    return
  }

  // Drop piece as far as possible
  while (!isCollision(0, 1)) {
    currentPiece.y++
  }

  // Lock piece and check for completed lines
  lockPiece()
  checkLines()

  // Spawn next piece if game isn't over
  if (!isGameOver()) {
    currentPiece = nextPiece
    createNextPiece()
  }
}

// Toggle pause state
function togglePause() {
  isPaused = !isPaused
  if (isPaused) {
    ctx.fillStyle = 'rgba(0, 0, 0, 0.75)'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    ctx.fillStyle = '#fff'
    ctx.font = '30px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('PAUSED', canvas.width / 2, canvas.height / 2)
  } else {
    requestAnimationFrame(update)
  }
}

// Mobile touch controls
function initTouchControls() {
  let touchStartX = 0
  let touchStartY = 0

  document.addEventListener(
    'touchstart',
    (e) => {
      touchStartX = e.touches[0].clientX
      touchStartY = e.touches[0].clientY
    },
    false,
  )

  document.addEventListener(
    'touchend',
    (e) => {
      if (isPaused || gameOver) return

      const touchEndX = e.changedTouches[0].clientX
      const touchEndY = e.changedTouches[0].clientY
      const dx = touchEndX - touchStartX
      const dy = touchEndY - touchStartY

      // Horizontal swipe threshold
      if (Math.abs(dx) > 30) {
        if (dx > 0) {
          moveRight()
        } else {
          moveLeft()
        }
      }

      // Vertical swipe threshold
      if (dy < -30) {
        rotate()
      } else if (dy > 30) {
        moveDown()
      }

      // Tap for hard drop
      if (Math.abs(dx) < 10 && Math.abs(dy) < 10) {
        hardDrop()
      }
    },
    false,
  )
}

// Initialize input when DOM is loaded
document.addEventListener('DOMContentLoaded', initInput)
