:root {
    --bg-color: #2d3436;
    --text-color: #f5f6fa;
    --accent-color: #00cec9;
    --board-color: #1e272e;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-container {
    display: flex;
    gap: 20px;
    padding: 20px;
}

canvas {
    background-color: var(--board-color);
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

#game-board {
    width: 300px;
    height: 600px;
}

#next-piece {
    width: 100px;
    height: 100px;
    background-color: var(--board-color);
    margin-top: 10px;
}

.side-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 150px;
}

.score-display, .level-display {
    background-color: var(--board-color);
    padding: 15px;
    border-radius: 4px;
    text-align: center;
}

h2 {
    margin: 0 0 10px 0;
    color: var(--accent-color);
    font-size: 1.2rem;
}

@media (max-width: 600px) {
    .game-container {
        flex-direction: column;
    }
    
    .side-panel {
        flex-direction: row;
        width: 300px;
        justify-content: space-between;
    }
}
